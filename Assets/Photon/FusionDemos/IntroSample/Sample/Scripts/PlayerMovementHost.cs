using Fusion;
using UnityEngine;
using Unity.Cinemachine;

namespace FusionDemo {
  /// <summary>
  /// A simple networked player movement class for host/server mode with mouse rotation.
  /// </summary>
  [RequireComponent(typeof(NetworkCharacterController))]
  public class PlayerMovementHost : NetworkBehaviour {
    [Header("Movement Settings")]
    [SerializeField] private float mouseSensitivity = 2.0f;
    [SerializeField] private float minVerticalAngle = -80f;
    [SerializeField] private float maxVerticalAngle = 80f;

    [Header("Camera Settings")]
    [SerializeField] private Transform lookAtTarget;

    private NetworkCharacterController _cc;

    // Client-side only - vertical look rotation for camera
    private float _clientVerticalRotation;

    [Networked] private NetworkButtons NetworkButtons { get; set; }
    // Only horizontal rotation needs to be networked for movement
    [Networked] private float NetworkHorizontalRotation { get; set; }

    public override void Spawned() {
      // get the NetworkCharacterController reference
      _cc = GetBehaviour<NetworkCharacterController>();

      // If no lookAtTarget is assigned, try to find one automatically
      if (lookAtTarget == null) {
        // Look for a child transform named "LookAtTarget" or similar
        lookAtTarget = transform.Find("LookAtTarget");
        if (lookAtTarget == null) {
          // Create a default lookAtTarget as a child
          GameObject lookAtGO = new GameObject("LookAtTarget");
          lookAtGO.transform.SetParent(transform);
          lookAtGO.transform.localPosition = Vector3.up * 1.6f; // Approximate head height
          lookAtTarget = lookAtGO.transform;
        }
      }

      // Set up Cinemachine camera for local player only
      if (Object.HasInputAuthority) {
        SetupPlayerCamera();

        // Initialize client-side vertical rotation
        _clientVerticalRotation = 0f;

        // Lock cursor
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
      }
    }

    private void SetupPlayerCamera() {
      // Find the CinemachineCamera tagged "PlayerCam"
      GameObject playerCamObject = GameObject.FindGameObjectWithTag("PlayerCam");

      if (playerCamObject != null) {
        var playerCam = playerCamObject.GetComponent<CinemachineCamera>();

        if (playerCam != null) {
          // Use lookAtTarget for both Follow and LookAt to control camera from one transform
          playerCam.Follow = lookAtTarget;
          playerCam.LookAt = lookAtTarget;

          Debug.Log($"Assigned camera to local player: {gameObject.name}, using lookAtTarget: {lookAtTarget.name} for both Follow and LookAt");
        }
        else {
          Debug.LogWarning("PlayerCam GameObject found but no CinemachineCamera component attached!");
        }
      }
      else {
        Debug.LogWarning("No GameObject with tag 'PlayerCam' found in the scene!");
      }
    }

    public override void FixedUpdateNetwork() {
      if (GetInput<DemoNetworkInput>(out var input)) {

        // Update horizontal rotation for movement (networked)
        if (Object.HasInputAuthority) {
          NetworkHorizontalRotation += input.MouseX * mouseSensitivity;
        }

        // Apply networked horizontal rotation to player transform
        transform.rotation = Quaternion.Euler(0, NetworkHorizontalRotation, 0);

        // Calculate movement direction based on player rotation
        var moveDirection = Vector3.zero;

        // WASD movement relative to player's horizontal rotation
        if (input.IsDown(DemoNetworkInput.BUTTON_FORWARD)) {
          moveDirection += transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_BACKWARD)) {
          moveDirection -= transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_RIGHT)) {
          moveDirection += transform.right;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_LEFT)) {
          moveDirection -= transform.right;
        }

        // Move the character
        _cc.Move(moveDirection.normalized);

        NetworkButtons = input.Buttons;
      }
    }



    public override void Despawned(NetworkRunner runner, bool hasState) {
      // Unlock cursor when player is despawned
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
      }
    }

    public override void Render() {
      // Handle client-side vertical rotation for smooth camera look
      if (Object.HasInputAuthority) {
        // Update client-side vertical rotation for smooth camera movement
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        _clientVerticalRotation -= mouseY;
        _clientVerticalRotation = Mathf.Clamp(_clientVerticalRotation, minVerticalAngle, maxVerticalAngle);

        // Apply vertical rotation to lookAtTarget for camera
        if (lookAtTarget != null) {
          lookAtTarget.localRotation = Quaternion.Euler(_clientVerticalRotation, 0, 0);
        }
      }
      // Note: Horizontal rotation is handled in FixedUpdateNetwork and applied to transform
      // Remote players don't need special handling - they use the networked transform rotation
    }

    private void Update() {
      // Allow ESC key to unlock cursor for local player
      if (Object.HasInputAuthority && Input.GetKeyDown(KeyCode.Escape)) {
        if (Cursor.lockState == CursorLockMode.Locked) {
          Cursor.lockState = CursorLockMode.None;
          Cursor.visible = true;
        } else {
          Cursor.lockState = CursorLockMode.Locked;
          Cursor.visible = false;
        }
      }
    }
  }
}