using Fusion;
using UnityEngine;

namespace FusionDemo {
  /// <summary>
  /// A simple networked player movement class for host/server mode with mouse rotation.
  /// </summary>
  [RequireComponent(typeof(NetworkCharacterController))]
  public class PlayerMovementHost : NetworkBehaviour {
    [Header("Movement Settings")]
    [SerializeField] private float mouseSensitivity = 2.0f;
    [SerializeField] private float minVerticalAngle = -80f;
    [SerializeField] private float maxVerticalAngle = 80f;

    private NetworkCharacterController _cc;
    private Transform _cameraTransform;

    [Networked] private NetworkButtons NetworkButtons { get; set; }
    [Networked] private float NetworkYRotation { get; set; }
    [Networked] private float NetworkXRotation { get; set; }

    public override void Spawned() {
      // get the NetworkCharacterController reference
      _cc = GetBehaviour<NetworkCharacterController>();

      // Find camera transform for vertical rotation (look for main camera or child camera)
      _cameraTransform = Camera.main?.transform;
      if (_cameraTransform == null) {
        _cameraTransform = GetComponentInChildren<Camera>()?.transform;
      }

      // Lock cursor for local player only
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
      }
    }

    public override void FixedUpdateNetwork() {
      // If we received input from the input authority
      // The NetworkObject input authority AND the server/host will have the inputs
      if (GetInput<DemoNetworkInput>(out var input)) {

        // Handle mouse rotation (only for input authority)
        if (Object.HasInputAuthority) {
          // Horizontal rotation (Y-axis)
          NetworkYRotation += input.MouseX * mouseSensitivity;

          // Vertical rotation (X-axis) with limits
          NetworkXRotation -= input.MouseY * mouseSensitivity;
          NetworkXRotation = Mathf.Clamp(NetworkXRotation, minVerticalAngle, maxVerticalAngle);
        }

        // Apply horizontal rotation to player body
        transform.rotation = Quaternion.Euler(0, NetworkYRotation, 0);

        // Apply vertical rotation to camera if available
        if (_cameraTransform != null) {
          _cameraTransform.localRotation = Quaternion.Euler(NetworkXRotation, 0, 0);
        }

        // Calculate movement direction based on current rotation
        var dir = Vector3.zero;

        // Forward/Backward movement (W/S keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_FORWARD)) {
          dir += transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_BACKWARD)) {
          dir -= transform.forward;
        }

        // Strafe movement (A/D keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_RIGHT)) {
          dir += transform.right;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_LEFT)) {
          dir -= transform.right;
        }

        // Use a custom movement method that doesn't affect rotation
        MoveWithoutRotation(dir.normalized);

        // Store the current buttons to use them on the next FUN (FixedUpdateNetwork) call
        NetworkButtons = input.Buttons;
      }
    }

    private void MoveWithoutRotation(Vector3 direction) {
      // Store current rotation
      var currentRotation = transform.rotation;

      // Use NetworkCharacterController's Move method
      _cc.Move(direction);

      // Restore rotation to prevent NetworkCharacterController from changing it
      transform.rotation = currentRotation;
    }

    public override void Despawned(NetworkRunner runner, bool hasState) {
      // Unlock cursor when player is despawned
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
      }
    }

    private void Update() {
      // Allow ESC key to unlock cursor for local player
      if (Object.HasInputAuthority && Input.GetKeyDown(KeyCode.Escape)) {
        if (Cursor.lockState == CursorLockMode.Locked) {
          Cursor.lockState = CursorLockMode.None;
          Cursor.visible = true;
        } else {
          Cursor.lockState = CursorLockMode.Locked;
          Cursor.visible = false;
        }
      }
    }
  }
}