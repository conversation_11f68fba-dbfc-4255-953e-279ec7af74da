using Fusion;
using UnityEngine;
using Unity.Cinemachine;

namespace FusionDemo {
  /// <summary>
  /// A simple networked player movement class for host/server mode with mouse rotation.
  /// </summary>
  [RequireComponent(typeof(NetworkCharacterController))]
  public class PlayerMovementHost : NetworkBehaviour {
    [Header("Movement Settings")]
    [SerializeField] private float mouseSensitivity = 2.0f;
    [SerializeField] private float minVerticalAngle = -80f;
    [SerializeField] private float maxVerticalAngle = 80f;

    [Header("Camera Settings")]
    [SerializeField] private Transform lookAtTarget;

    private NetworkCharacterController _cc;

    // Client-side camera rotations (not networked)
    private float _cameraYaw;   // Horizontal camera rotation
    private float _cameraPitch; // Vertical camera rotation

    [Networked] private NetworkButtons NetworkButtons { get; set; }
    // Player body rotation for movement (networked)
    [Networked] private float NetworkBodyYaw { get; set; }

    public override void Spawned() {
      // get the NetworkCharacterController reference
      _cc = GetBehaviour<NetworkCharacterController>();

      // If no lookAtTarget is assigned, try to find one automatically
      if (lookAtTarget == null) {
        // Look for a child transform named "LookAtTarget" or similar
        lookAtTarget = transform.Find("LookAtTarget");
        if (lookAtTarget == null) {
          // Create a default lookAtTarget as a child
          GameObject lookAtGO = new GameObject("LookAtTarget");
          lookAtGO.transform.SetParent(transform);
          lookAtGO.transform.localPosition = Vector3.up * 1.6f; // Approximate head height
          lookAtTarget = lookAtGO.transform;
        }
      }

      // Set up Cinemachine camera for local player only
      if (Object.HasInputAuthority) {
        SetupPlayerCamera();

        // Initialize camera rotations
        _cameraYaw = 0f;
        _cameraPitch = 0f;

        // Lock cursor
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
      }
    }

    private void SetupPlayerCamera() {
      // Find the CinemachineCamera tagged "PlayerCam"
      GameObject playerCamObject = GameObject.FindGameObjectWithTag("PlayerCam");

      if (playerCamObject != null) {
        var playerCam = playerCamObject.GetComponent<CinemachineCamera>();

        if (playerCam != null) {
          // Use lookAtTarget for both Follow and LookAt to control camera from one transform
          playerCam.Follow = lookAtTarget;
          playerCam.LookAt = lookAtTarget;

          Debug.Log($"Assigned camera to local player: {gameObject.name}, using lookAtTarget: {lookAtTarget.name} for both Follow and LookAt");
        }
        else {
          Debug.LogWarning("PlayerCam GameObject found but no CinemachineCamera component attached!");
        }
      }
      else {
        Debug.LogWarning("No GameObject with tag 'PlayerCam' found in the scene!");
      }
    }

    public override void FixedUpdateNetwork() {
      if (GetInput<DemoNetworkInput>(out var input)) {

        // Update camera yaw from mouse input (this drives body rotation)
        if (Object.HasInputAuthority) {
          _cameraYaw += input.MouseX * mouseSensitivity;

          // Update networked body rotation to match camera yaw for movement
          NetworkBodyYaw = _cameraYaw;
        }

        // Apply body rotation for movement calculations
        transform.rotation = Quaternion.Euler(0, NetworkBodyYaw, 0);

        // Calculate movement direction relative to camera/body rotation
        var moveDirection = Vector3.zero;

        // WASD movement relative to current body rotation
        if (input.IsDown(DemoNetworkInput.BUTTON_FORWARD)) {
          moveDirection += transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_BACKWARD)) {
          moveDirection -= transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_RIGHT)) {
          moveDirection += transform.right;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_LEFT)) {
          moveDirection -= transform.right;
        }

        // Move the character
        _cc.Move(moveDirection.normalized);

        NetworkButtons = input.Buttons;
      }
    }



    public override void Despawned(NetworkRunner runner, bool hasState) {
      // Unlock cursor when player is despawned
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
      }
    }

    public override void Render() {
      if (Object.HasInputAuthority) {
        // Update camera rotations with smooth mouse input
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;

        // Update camera yaw and pitch smoothly
        _cameraYaw += mouseX;
        _cameraPitch -= mouseY;
        _cameraPitch = Mathf.Clamp(_cameraPitch, minVerticalAngle, maxVerticalAngle);

        // Apply smooth body rotation (horizontal only)
        transform.rotation = Quaternion.Euler(0, _cameraYaw, 0);

        // Apply camera pitch to lookAtTarget
        if (lookAtTarget != null) {
          lookAtTarget.localRotation = Quaternion.Euler(_cameraPitch, 0, 0);
        }
      } else {
        // For remote players, smoothly interpolate to networked body rotation
        float targetYaw = NetworkBodyYaw;
        float currentYaw = transform.eulerAngles.y;
        float smoothYaw = Mathf.LerpAngle(currentYaw, targetYaw, Time.deltaTime * 10f);

        transform.rotation = Quaternion.Euler(0, smoothYaw, 0);
      }
    }

    private void Update() {
      // Allow ESC key to unlock cursor for local player
      if (Object.HasInputAuthority && Input.GetKeyDown(KeyCode.Escape)) {
        if (Cursor.lockState == CursorLockMode.Locked) {
          Cursor.lockState = CursorLockMode.None;
          Cursor.visible = true;
        } else {
          Cursor.lockState = CursorLockMode.Locked;
          Cursor.visible = false;
        }
      }
    }
  }
}