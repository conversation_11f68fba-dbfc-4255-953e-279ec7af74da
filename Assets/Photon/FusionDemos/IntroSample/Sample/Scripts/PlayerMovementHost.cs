using Fusion;
using UnityEngine;
using Unity.Cinemachine;

namespace FusionDemo {
  /// <summary>
  /// A simple networked player movement class for host/server mode with mouse rotation.
  /// </summary>
  [RequireComponent(typeof(NetworkCharacterController))]
  public class PlayerMovementHost : NetworkBehaviour {
    [Header("Movement Settings")]
    [SerializeField] private float mouseSensitivity = 2.0f;
    [SerializeField] private float minVerticalAngle = -80f;
    [SerializeField] private float maxVerticalAngle = 80f;

    [Header("Camera Settings")]
    [SerializeField] private Transform lookAtTarget;

    private NetworkCharacterController _cc;

    // Local rotation values for smooth interpolation
    private float _localYRotation;
    private float _localXRotation;

    [Networked] private NetworkButtons NetworkButtons { get; set; }
    [Networked] private float NetworkYRotation { get; set; }
    [Networked] private float NetworkXRotation { get; set; }

    public override void Spawned() {
      // get the NetworkCharacterController reference
      _cc = GetBehaviour<NetworkCharacterController>();

      // If no lookAtTarget is assigned, try to find one automatically
      if (lookAtTarget == null) {
        // Look for a child transform named "LookAtTarget" or similar
        lookAtTarget = transform.Find("LookAtTarget");
        if (lookAtTarget == null) {
          // Create a default lookAtTarget as a child
          GameObject lookAtGO = new GameObject("LookAtTarget");
          lookAtGO.transform.SetParent(transform);
          lookAtGO.transform.localPosition = Vector3.up * 1.6f; // Approximate head height
          lookAtTarget = lookAtGO.transform;
        }
      }

      // Set up Cinemachine camera for local player only
      if (Object.HasInputAuthority) {
        SetupPlayerCamera();

        // Initialize local rotation values
        _localYRotation = NetworkYRotation;
        _localXRotation = NetworkXRotation;

        // Lock cursor
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
      } else {
        // For remote players, initialize local rotation values to match networked values
        _localYRotation = NetworkYRotation;
        _localXRotation = NetworkXRotation;
      }
    }

    private void SetupPlayerCamera() {
      // Find the CinemachineCamera tagged "PlayerCam"
      GameObject playerCamObject = GameObject.FindGameObjectWithTag("PlayerCam");

      if (playerCamObject != null) {
        var playerCam = playerCamObject.GetComponent<CinemachineCamera>();

        if (playerCam != null) {
          // Use lookAtTarget for both Follow and LookAt to control camera from one transform
          playerCam.Follow = lookAtTarget;
          playerCam.LookAt = lookAtTarget;

          Debug.Log($"Assigned camera to local player: {gameObject.name}, using lookAtTarget: {lookAtTarget.name} for both Follow and LookAt");
        }
        else {
          Debug.LogWarning("PlayerCam GameObject found but no CinemachineCamera component attached!");
        }
      }
      else {
        Debug.LogWarning("No GameObject with tag 'PlayerCam' found in the scene!");
      }
    }

    public override void FixedUpdateNetwork() {
      // If we received input from the input authority
      // The NetworkObject input authority AND the server/host will have the inputs
      if (GetInput<DemoNetworkInput>(out var input)) {

        // Handle mouse rotation (only for input authority)
        if (Object.HasInputAuthority) {
          // Horizontal rotation (Y-axis)
          NetworkYRotation += input.MouseX * mouseSensitivity;

          // Vertical rotation (X-axis) with limits
          NetworkXRotation -= input.MouseY * mouseSensitivity;
          NetworkXRotation = Mathf.Clamp(NetworkXRotation, minVerticalAngle, maxVerticalAngle);
        }

        // Calculate movement direction based on current rotation
        var dir = Vector3.zero;

        // Forward/Backward movement (W/S keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_FORWARD)) {
          dir += transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_BACKWARD)) {
          dir -= transform.forward;
        }

        // Strafe movement (A/D keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_RIGHT)) {
          dir += transform.right;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_LEFT)) {
          dir -= transform.right;
        }

        // Use a custom movement method that doesn't affect rotation
        MoveWithoutRotation(dir.normalized);

        // Store the current buttons to use them on the next FUN (FixedUpdateNetwork) call
        NetworkButtons = input.Buttons;
      }
    }

    private void MoveWithoutRotation(Vector3 direction) {
      // Store current rotation
      var currentRotation = transform.rotation;

      // Use NetworkCharacterController's Move method
      _cc.Move(direction);

      // Restore rotation to prevent NetworkCharacterController from changing it
      transform.rotation = currentRotation;
    }

    public override void Despawned(NetworkRunner runner, bool hasState) {
      // Unlock cursor when player is despawned
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
      }
    }

    public override void Render() {
      // Smooth interpolation for local player
      if (Object.HasInputAuthority) {
        // Apply immediate mouse input for responsive feel
        _localYRotation += Input.GetAxis("Mouse X") * mouseSensitivity;
        _localXRotation -= Input.GetAxis("Mouse Y") * mouseSensitivity;
        _localXRotation = Mathf.Clamp(_localXRotation, minVerticalAngle, maxVerticalAngle);

        // Apply rotations immediately for smooth visuals
        transform.rotation = Quaternion.Euler(0, _localYRotation, 0);
        if (lookAtTarget != null) {
          lookAtTarget.localRotation = Quaternion.Euler(_localXRotation, 0, 0);
        }
      } else {
        // For remote players, interpolate towards networked values
        _localYRotation = Mathf.LerpAngle(_localYRotation, NetworkYRotation, Time.deltaTime * 10f);
        _localXRotation = Mathf.Lerp(_localXRotation, NetworkXRotation, Time.deltaTime * 10f);

        // Apply interpolated rotations
        transform.rotation = Quaternion.Euler(0, _localYRotation, 0);
        if (lookAtTarget != null) {
          lookAtTarget.localRotation = Quaternion.Euler(_localXRotation, 0, 0);
        }
      }
    }

    private void Update() {
      // Allow ESC key to unlock cursor for local player
      if (Object.HasInputAuthority && Input.GetKeyDown(KeyCode.Escape)) {
        if (Cursor.lockState == CursorLockMode.Locked) {
          Cursor.lockState = CursorLockMode.None;
          Cursor.visible = true;
        } else {
          Cursor.lockState = CursorLockMode.Locked;
          Cursor.visible = false;
        }
      }
    }
  }
}