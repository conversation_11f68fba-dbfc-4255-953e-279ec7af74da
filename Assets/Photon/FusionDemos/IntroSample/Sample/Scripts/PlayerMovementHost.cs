using Fusion;
using UnityEngine;

namespace FusionDemo {
  /// <summary>
  /// A simple networked player movement class for host/server mode with mouse rotation.
  /// </summary>
  [RequireComponent(typeof(NetworkCharacterController))]
  public class PlayerMovementHost : NetworkBehaviour {
    [Header("Movement Settings")]
    [SerializeField] private float mouseSensitivity = 2.0f;

    private NetworkCharacterController _cc;

    [Networked] private NetworkButtons NetworkButtons { get; set; }
    [Networked] private float NetworkYRotation { get; set; }

    public override void Spawned() {
      // get the NetworkCharacterController reference
      _cc = GetBehaviour<NetworkCharacterController>();

      // Lock cursor for local player only
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
      }
    }

    public override void FixedUpdateNetwork() {
      // If we received input from the input authority
      // The NetworkObject input authority AND the server/host will have the inputs
      if (GetInput<DemoNetworkInput>(out var input)) {

        // Handle mouse rotation
        if (Object.HasInputAuthority) {
          NetworkYRotation += input.MouseX * mouseSensitivity;
        }

        // Apply rotation to transform
        transform.rotation = Quaternion.Euler(0, NetworkYRotation, 0);

        // Calculate movement direction based on current rotation
        var dir = Vector3.zero;

        // Forward/Backward movement (W/S keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_FORWARD)) {
          dir += transform.forward;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_BACKWARD)) {
          dir -= transform.forward;
        }

        // Strafe movement (A/D keys)
        if (input.IsDown(DemoNetworkInput.BUTTON_RIGHT)) {
          dir += transform.right;
        }
        if (input.IsDown(DemoNetworkInput.BUTTON_LEFT)) {
          dir -= transform.right;
        }

        // Move with the direction calculated (NetworkCharacterController handles the movement)
        // We don't normalize here because NetworkCharacterController.Move expects world-space direction
        _cc.Move(dir.normalized);

        // Store the current buttons to use them on the next FUN (FixedUpdateNetwork) call
        NetworkButtons = input.Buttons;
      }
    }

    public override void Despawned(NetworkRunner runner, bool hasState) {
      // Unlock cursor when player is despawned
      if (Object.HasInputAuthority) {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
      }
    }

    private void Update() {
      // Allow ESC key to unlock cursor for local player
      if (Object.HasInputAuthority && Input.GetKeyDown(KeyCode.Escape)) {
        if (Cursor.lockState == CursorLockMode.Locked) {
          Cursor.lockState = CursorLockMode.None;
          Cursor.visible = true;
        } else {
          Cursor.lockState = CursorLockMode.Locked;
          Cursor.visible = false;
        }
      }
    }
  }
}