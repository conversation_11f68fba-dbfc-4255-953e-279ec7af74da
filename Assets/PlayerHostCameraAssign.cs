using UnityEngine;
using Fusion;
using Unity.Cinemachine;

public class PlayerHostCameraAssign : NetworkBehaviour
{
    // This script is now deprecated - camera assignment is handled in PlayerMovementHost
    // Keeping this for backward compatibility, but it won't do anything

    public override void Spawned()
    {
        // Camera assignment is now handled in PlayerMovementHost.cs
        // This script can be removed if not needed for other purposes
        Debug.Log("PlayerHostCameraAssign: Camera assignment is now handled by PlayerMovementHost script.");
    }
}
