using UnityEngine;
using Fusion;
using Unity.Cinemachine;

public class PlayerHostCameraAssign : NetworkBehaviour
{
    public override void Spawned()
    {
        // Only assign the camera if this NetworkObject belongs to the local player
        if (Object.HasInputAuthority)
        {
            AssignPlayerCamera();
        }
    }

    private void AssignPlayerCamera()
    {
        // Find the CinemachineCamera tagged "PlayerCam"
        GameObject playerCamObject = GameObject.FindGameObjectWithTag("PlayerCam");

        if (playerCamObject != null)
        {
            CinemachineCamera playerCam = playerCamObject.GetComponent<CinemachineCamera>();

            if (playerCam != null)
            {
                // Set this player's transform as the camera's follow and look at target
                playerCam.Follow = transform;
                playerCam.LookAt = transform;

                Debug.Log($"Assigned camera to local player: {gameObject.name}");
            }
            else
            {
                Debug.LogWarning("PlayerCam GameObject found but no CinemachineCamera component attached!");
            }
        }
        else
        {
            Debug.LogWarning("No GameObject with tag 'PlayerCam' found in the scene!");
        }
    }
}
